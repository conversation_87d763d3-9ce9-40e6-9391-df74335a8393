import * as admin from "firebase-admin";
import { CollectionEntity, CollectionStatus } from "../types";
import { log } from "../utils/logger";

export const DEADLINE_DAYS = 7;
export const DEADLINE_MS = DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export const LIMITED_COLLECTION_DEADLINE_DAYS = 30;
export const LIMITED_COLLECTION_DEADLINE_MS =
  LIMITED_COLLECTION_DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export function createDeadline(days: number = DEADLINE_DAYS) {
  const deadlineMs = days * 24 * 60 * 60 * 1000;
  const deadline = new Date(Date.now() + deadlineMs);
  return admin.firestore.Timestamp.fromDate(deadline);
}

export function createStandardDeadline() {
  return createDeadline(DEADLINE_DAYS);
}

export function createLimitedCollectionDeadline() {
  return createDeadline(LIMITED_COLLECTION_DEADLINE_DAYS);
}

export async function addDeadlineIfMarketCollection(
  db: admin.firestore.Firestore,
  collectionId: string,
  orderId: string,
  updateData: any
) {
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  if (collectionDoc.exists) {
    const collection = collectionDoc.data() as CollectionEntity;
    if (collection.status === CollectionStatus.MARKET) {
      updateData.deadline = createStandardDeadline();
      log.monitorLog(`Added deadline to order for MARKET collection`, {
        monitor: "deadline_service",
        deadlineDays: DEADLINE_DAYS,
        orderId,
        collectionId,
        status: "deadline_added",
      });
    }
  }
}

export function calculateOrderDeadline(collection: CollectionEntity | null) {
  // If collection doesn't have launchedAt, set deadline to null
  if (!collection?.launchedAt) {
    return null;
  }

  const now = Date.now();
  const currentDatePlus7Days = now + 7 * 24 * 60 * 60 * 1000;

  // Calculate launchedAt + 28 days
  const launchedAtMs = collection.launchedAt.toMillis();
  const launchedAtPlus28Days = launchedAtMs + 28 * 24 * 60 * 60 * 1000;

  // Use the earlier of the two dates
  const deadlineMs = Math.min(launchedAtPlus28Days, currentDatePlus7Days);

  return admin.firestore.Timestamp.fromMillis(deadlineMs);
}

export function isOrderExpired(deadline: admin.firestore.Timestamp) {
  const now = new Date();
  const deadlineDate = deadline.toDate();
  return now > deadlineDate;
}

export async function getExpiredOrders(db: admin.firestore.Firestore) {
  const now = admin.firestore.Timestamp.now();

  const expiredOrdersQuery = await db
    .collection("orders")
    .where("deadline", "<=", now)
    .where("status", "==", "paid")
    .get();

  return expiredOrdersQuery.docs;
}

export async function addDeadlineToOrders(
  db: admin.firestore.Firestore,
  collectionId: string
) {
  const ordersQuery = await db
    .collection("orders")
    .where("collectionId", "==", collectionId)
    .where("status", "==", "paid")
    .where("deadline", "==", null)
    .get();

  const ordersToUpdate = ordersQuery.docs.filter((doc) => !doc.data().deadline);

  if (ordersToUpdate.length === 0) {
    log.monitorLog(`No orders need deadline updates for collection`, {
      monitor: "deadline_service",
      collectionId,
      status: "no_updates_needed",
    });
    return 0;
  }

  const BATCH_SIZE = 400;
  let totalUpdatedCount = 0;

  for (let i = 0; i < ordersToUpdate.length; i += BATCH_SIZE) {
    const chunk = ordersToUpdate.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      batch.update(orderDoc.ref, {
        deadline: createLimitedCollectionDeadline(),
      });
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;

    log.monitorLog(`Processed batch for deadline updates`, {
      monitor: "deadline_service",
      batchNumber: Math.floor(i / BATCH_SIZE) + 1,
      updatedCount: chunk.length,
      status: "batch_processed",
    });
  }

  log.monitorLog(`Added deadlines to orders for collection`, {
    monitor: "deadline_service",
    totalUpdatedCount,
    collectionId,
    status: "deadlines_added_complete",
  });

  return totalUpdatedCount;
}

export function getTimeUntilDeadline(deadline: admin.firestore.Timestamp) {
  const now = new Date();
  const deadlineDate = deadline.toDate();
  const timeDiff = deadlineDate.getTime() - now.getTime();

  if (timeDiff <= 0) {
    return { days: 0, hours: 0, minutes: 0, isExpired: true };
  }

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

  return { days, hours, minutes, isExpired: false };
}
