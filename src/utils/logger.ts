import winston from "winston";
import { LoggingWinston } from "@google-cloud/logging-winston";

function createLoggingWinston(): LoggingWinston {
  const projectId =
    process.env.GCLOUD_PROJECT ??
    process.env.GCP_PROJECT ??
    process.env.FIREBASE_PROJECT_ID;
  const keyFilename = process.env.GOOGLE_APPLICATION_CREDENTIALS;

  const config: any = {
    logName: "marketplace-bot",
  };

  if (projectId) {
    config.projectId = projectId;
  }

  if (keyFilename) {
    config.keyFilename = keyFilename;
  }

  return new LoggingWinston(config);
}

const loggingWinston = createLoggingWinston();

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL ?? "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: "marketplace-bot",
    environment: process.env.NODE_ENV ?? "development",
  },
  transports: [
    // Always add console transport for local development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
  ],
});

if (
  process.env.NODE_ENV === "production" ||
  process.env.ENABLE_CLOUD_LOGGING === "true"
) {
  logger.add(loggingWinston);
}

interface LogContext {
  userId?: string;
  chatId?: string;
  messageId?: string;
  operation?: string;
  [key: string]: any;
}

class Logger {
  private readonly winston: winston.Logger;

  constructor(winstonLogger: winston.Logger) {
    this.winston = winstonLogger;
  }

  info(message: string, context?: LogContext) {
    this.winston.info(message, context);
  }

  error(message: string, error?: Error | any, context?: LogContext) {
    const logData = {
      ...context,
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : error,
    };
    this.winston.error(message, logData);
  }

  warn(message: string, context?: LogContext) {
    this.winston.warn(message, context);
  }

  debug(message: string, context?: LogContext) {
    this.winston.debug(message, context);
  }

  // Specialized logging methods for bot operations
  botLog(
    message: string,
    botData: {
      userId?: string;
      chatId?: string;
      messageId?: string;
      operation?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "bot_operation",
      ...botData,
    });
  }

  webhookLog(
    message: string,
    webhookData: {
      method?: string;
      url?: string;
      status?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "webhook_operation",
      ...webhookData,
    });
  }

  healthLog(
    message: string,
    healthData: {
      status?: string;
      timestamp?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "health_check",
      ...healthData,
    });
  }

  redisLog(
    message: string,
    redisData: {
      operation?: string;
      key?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "redis_operation",
      ...redisData,
    });
  }
}

// Export singleton logger instance
export const log = new Logger(logger);

// Export types for use in other files
export type { LogContext };
